{"name": "xdt-front-system", "version": "2.1.0", "private": true, "license": "MIT", "description": "An out-of-box UI solution for enterprise applications", "scripts": {"analyze": "cross-env ANALYZE=1 umi build", "build": "umi build", "deploy": "npm run build && npm run gh-pages", "dev": "cross-env NODE_OPTIONS=--openssl-legacy-provider && npm run start:dev", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "umi g tmp", "lint": "umi g tmp && npm run lint:js && npm run lint:style && npm run lint:prettier", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src && npm run lint:style", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier -c --write \"src/**/*\" --end-of-line auto", "lint:style": "stylelint --fix \"src/**/*.less\" --syntax less", "openapi": "umi open<PERSON>i", "precommit": "lint-staged", "prettier": "prettier -c --write \"src/**/*\"", "start": "cross-env NODE_OPTIONS=--openssl-legacy-provider UMI_ENV=dev umi dev", "start:dev": "cross-env NODE_OPTIONS=--openssl-legacy-provider REACT_APP_ENV=dev MOCK=none UMI_ENV=dev umi dev", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev umi dev", "start:no-ui": "cross-env UMI_UI=none UMI_ENV=dev umi dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev umi dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev umi dev", "pretest": "node ./tests/beforeTest", "test": "umi test", "test:all": "node ./tests/run-tests.js", "test:component": "umi test ./src/components", "serve": "umi-serve", "tsc": "tsc --noEmit", "husky": "npx husky install", "update": "yarn upgrade-interactive --latest", "delnode": "rimraf node_modules"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@ant-design/compatible": "^1.0.2", "@ant-design/icons": "^4.7.0", "@ant-design/pro-card": "^1.19.0", "@ant-design/pro-components": "^2.6.13", "@ant-design/pro-descriptions": "^1.10.0", "@ant-design/pro-form": "^1.64.0", "@ant-design/pro-layout": "^6.35.0", "@ant-design/pro-table": "^2.71.0", "@antv/data-set": "^0.11.1", "@umijs/route-utils": "^2.0.0", "ahooks": "^3.7.6", "antd": "~4.22.0", "array-move": "^4.0.0", "bizcharts": "^4.0.12", "caniuse-lite": "^1.0.30001279", "classnames": "^2.3.0", "crypto-js": "^4.0.0", "decimal.js": "^10.4.3", "echarts": "^4.7.0", "echarts-for-react": "^2.0.16", "echarts-wordcloud": "^1.1.3", "eslint-import-resolver-webpack": "^0.13.1", "js-export-excel": "^1.1.4", "lodash": "^4.17.0", "moment": "^2.29.0", "mqtt": "^3.0.0", "numeral": "^2.0.6", "omit.js": "^2.0.2", "path-to-regexp": "2.4.0", "prop-types": "^15.7.2", "qr-code-with-logo": "^1.1.0", "qrcode": "^1.4.4", "qs": "^6.11.2", "rc-menu": "^9.1.0", "rc-util": "^5.16.0", "react": "^17.0.0", "react-color": "^2.18.1", "react-copy-to-clipboard": "^5.0.4", "react-countup": "^4.3.3", "react-cropper": "^2.1.7", "react-dev-inspector": "^1.7.0", "react-dnd": "14.0.5", "react-dnd-html5-backend": "^16.0.1", "react-document-title": "^2.0.3", "react-dom": "^17.0.0", "react-fittext": "^1.0.0", "react-helmet-async": "^1.2.0", "react-quill": "^1.3.5", "react-sortable-hoc": "^2.0.0", "scriptjs": "^2.5.9", "umi": "^3.5.0", "umi-serve": "^1.9.10", "use-merge-value": "^1.0.1"}, "devDependencies": {"@ant-design/pro-cli": "^2.1.0", "@playwright/test": "^1.17.0", "@types/express": "^4.17.0", "@types/history": "^4.7.0", "@types/jest": "^26.0.0", "@types/lodash": "^4.14.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/react-helmet": "^6.1.0", "@umijs/fabric": "^2.8.0", "@umijs/openapi": "^1.3.0", "@umijs/plugin-blocks": "^2.2.0", "@umijs/plugin-esbuild": "^1.4.0", "@umijs/plugin-openapi": "^1.3.0", "@umijs/plugin-qiankun": "^2.42.0", "@umijs/preset-ant-design-pro": "^1.3.0", "@umijs/preset-dumi": "^1.1.0", "@umijs/preset-react": "^1.8.17", "@umijs/yorkie": "^2.0.3", "autoprefixer": "^10.4.0", "babel-plugin-import": "^1.13.8", "babel-plugin-transform-remove-console": "^6.9.4", "babel-plugin-transform-remove-debugger": "^6.9.4", "browserslist": "^4.17.6", "carlo": "^0.9.46", "cross-env": "^7.0.0", "cross-port-killer": "^1.3.0", "detect-installer": "^1.0.0", "enzyme": "^3.11.0", "eslint": "^7.32.0", "express": "^4.17.1", "gh-pages": "^3.2.0", "husky": "^8.0.3", "jsdom-global": "^3.0.0", "lint-staged": "^10.0.0", "mockjs": "^1.1.0", "prettier": "^2.5.0", "puppeteer-core": "^8.0.0", "stylelint": "^13.0.0", "swagger-ui-react": "^3.52.0", "typescript": "^4.5.0", "umi-serve": "^1.9.10"}, "engines": {"node": ">=12.0.0"}, "lint-staged": {"**/*.less": "stylelint --syntax less", "**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "gitHooks": {"commit-msg": "node scripts/verifyCommitMsg.js"}}